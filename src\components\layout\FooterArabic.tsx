"use client";

import Link from "next/link";
import { Calcula<PERSON>, BookO<PERSON>, HelpCircle, Heart } from "lucide-react";

interface FooterArabicProps {
  dict: any;
}

export function FooterArabic({ dict }: FooterArabicProps) {
  const currentYear = new Date().getFullYear();

  const navigation = [
    {
      name: dict.navigation?.home || 'الحاسبة',
      href: '/ar',
      icon: Calculator,
    },
    {
      name: dict.navigation?.blog || 'المدونة',
      href: '/ar/blog',
      icon: BookOpen,
    },
    {
      name: dict.navigation?.faq || 'الأسئلة الشائعة',
      href: '/ar/faq',
      icon: HelpCircle,
    }
  ];

  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {/* Brand */}
          <div className="space-y-6 text-right">
            <Link href="/ar" className="flex items-center space-x-0 space-x-reverse justify-end">
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-primary text-primary-foreground ml-3">
                <Calculator className="h-6 w-6" />
              </div>
              <span className="text-2xl font-bold text-primary font-arabic-display">RibaCalc</span>
            </Link>
            <p className="text-base text-muted-foreground max-w-sm text-right font-arabic leading-relaxed">
              {dict.footer?.description || 'حاسبة قروض مجانية للمغرب بالدرهم المغربي. احسب الفوائد الثابتة والدفعات الشهرية والسداد الإجمالي.'}
            </p>
          </div>

          {/* Navigation */}
          <div className="space-y-4 text-right">
            <h3 className="text-sm font-semibold text-foreground text-right">
              {dict.footer?.quickLinks || 'روابط سريعة'}
            </h3>
            <ul className="space-y-2">
              {navigation.map((item) => {
                const Icon = item.icon;
                return (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className="flex items-center justify-end space-x-0 space-x-reverse text-right text-sm text-muted-foreground hover:text-primary transition-colors"
                    >
                      <Icon className="h-4 w-4 mr-2" />
                      <span>{item.name}</span>
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>

          {/* Legal */}
          <div className="space-y-4 text-right">
            <h3 className="text-sm font-semibold text-foreground text-right">
              {dict.footer?.legal || 'قانوني'}
            </h3>
            <ul className="space-y-2">
              <li>
                <span className="text-sm text-muted-foreground text-right block">
                  {dict.footer?.educationalPurpose || 'لأغراض تعليمية فقط'}
                </span>
              </li>
              <li>
                <span className="text-sm text-muted-foreground text-right block">
                  {dict.footer?.notFinancialAdvice || 'ليست نصيحة مالية'}
                </span>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm text-muted-foreground text-right w-full md:w-auto">
              © {currentYear} RibaCalc. {dict.footer?.allRightsReserved || 'جميع الحقوق محفوظة'}.
            </p>
            <p className="text-sm text-muted-foreground flex items-center space-x-0 space-x-reverse">
              <span>{dict.footer?.forMoroccoResidents || 'لسكان المغرب'}</span>
              <Heart className="h-4 w-4 text-red-500 mx-1" />
              <span>{dict.footer?.madeWith || 'صنع بـ'}</span>
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
