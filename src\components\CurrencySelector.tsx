'use client'

import { useState, useEffect } from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ChevronDown, Banknote, MapPin } from 'lucide-react'
import { getCurrencyList, getCurrency, type Currency } from '@/lib/currency'
import { smartCurrencyDetection } from '@/lib/geolocation'

interface CurrencySelectorProps {
  selectedCurrency: string;
  onCurrencyChange: (currencyCode: string) => void;
  lang: 'en' | 'fr' | 'ar';
  dict: any;
  autoDetect?: boolean;
}

export default function CurrencySelector({
  selectedCurrency,
  onCurrencyChange,
  lang,
  dict,
  autoDetect = true
}: CurrencySelectorProps) {
  const [isDetecting, setIsDetecting] = useState(false)
  const [detectedCurrency, setDetectedCurrency] = useState<string | null>(null)
  const currencies = getCurrencyList()
  const currentCurrency = getCurrency(selectedCurrency)

  // Auto-detect currency on component mount
  useEffect(() => {
    if (autoDetect && !detectedCurrency) {
      setIsDetecting(true)
      smartCurrencyDetection()
        .then((currency) => {
          setDetectedCurrency(currency)
          // Only auto-set if user hasn't manually selected a currency
          if (selectedCurrency === 'MAD') { // Default currency
            onCurrencyChange(currency)
          }
        })
        .catch((error) => {
          console.error('Currency detection failed:', error)
          setDetectedCurrency('MAD')
        })
        .finally(() => {
          setIsDetecting(false)
        })
    }
  }, [autoDetect, detectedCurrency, selectedCurrency, onCurrencyChange])

  // Group currencies by region for better organization
  const gccCurrencies = currencies.filter(c =>
    ['AED', 'SAR', 'KWD', 'QAR', 'BHD', 'OMR'].includes(c.code)
  )
  const maghrebCurrencies = currencies.filter(c =>
    ['MAD', 'TND', 'DZD', 'LYD'].includes(c.code)
  )
  const mashreqCurrencies = currencies.filter(c =>
    ['EGP', 'JOD', 'LBP', 'SYP'].includes(c.code)
  )
  const otherArabCurrencies = currencies.filter(c =>
    ['IQD', 'YER', 'SDG'].includes(c.code)
  )
  const internationalCurrencies = currencies.filter(c =>
    ['USD', 'EUR'].includes(c.code)
  )

  const getCountryFlag = (countryCode: string) => {
    // Convert country code to flag emoji
    const codePoints = countryCode
      .toUpperCase()
      .split('')
      .map(char => 127397 + char.charCodeAt(0))
    return String.fromCodePoint(...codePoints)
  }

  const CurrencyGroup = ({ 
    title, 
    currencies 
  }: { 
    title: string; 
    currencies: Currency[] 
  }) => (
    <>
      {currencies.length > 0 && (
        <>
          <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground border-b">
            {title}
          </div>
          {currencies.map((currency) => (
            <DropdownMenuItem
              key={currency.code}
              onClick={() => onCurrencyChange(currency.code)}
              className={`flex items-center gap-3 px-3 py-2 cursor-pointer ${
                selectedCurrency === currency.code ? 'bg-accent' : ''
              }`}
            >
              <span className="text-lg">
                {getCountryFlag(currency.countryCode)}
              </span>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">
                    {currency.name[lang]}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {currency.code}
                  </Badge>
                </div>
                <div className="text-xs text-muted-foreground truncate">
                  {currency.country[lang]}
                </div>
              </div>
              <span className="text-sm font-mono text-muted-foreground">
                {currency.symbol}
              </span>
            </DropdownMenuItem>
          ))}
        </>
      )}
    </>
  )

  return (
    <div className="w-full">
      <label className="block text-sm font-medium mb-2">
        {dict.calculator?.currency || 'Currency'}
      </label>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-between h-12 px-4"
            dir={lang === 'ar' ? 'rtl' : 'ltr'}
            disabled={isDetecting}
          >
            <div className="flex items-center gap-3">
              {isDetecting ? (
                <MapPin className="h-4 w-4 text-muted-foreground animate-pulse" />
              ) : (
                <Banknote className="h-4 w-4 text-muted-foreground" />
              )}
              {isDetecting ? (
                <span className="text-sm text-muted-foreground">
                  {lang === 'ar' ? 'جاري تحديد موقعك...' :
                   lang === 'fr' ? 'Détection de votre localisation...' :
                   'Detecting your location...'}
                </span>
              ) : currentCurrency ? (
                <>
                  <span className="text-lg">
                    {getCountryFlag(currentCurrency.countryCode)}
                  </span>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">
                      {currentCurrency.name[lang]}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {currentCurrency.code}
                    </Badge>
                    {detectedCurrency === currentCurrency.code && (
                      <Badge variant="secondary" className="text-xs">
                        {lang === 'ar' ? 'تلقائي' :
                         lang === 'fr' ? 'Auto' :
                         'Auto'}
                      </Badge>
                    )}
                  </div>
                </>
              ) : null}
            </div>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          className="w-80 max-h-96 overflow-y-auto"
          align={lang === 'ar' ? 'end' : 'start'}
        >
          <CurrencyGroup
            title={dict.regions?.international || "International Currencies"}
            currencies={internationalCurrencies}
          />
          <CurrencyGroup
            title={dict.regions?.gcc || "Gulf Cooperation Council"}
            currencies={gccCurrencies}
          />
          <CurrencyGroup
            title={dict.regions?.maghreb || "Maghreb"}
            currencies={maghrebCurrencies}
          />
          <CurrencyGroup
            title={dict.regions?.mashreq || "Mashreq"}
            currencies={mashreqCurrencies}
          />
          <CurrencyGroup
            title={dict.regions?.other || "Other Arab Countries"}
            currencies={otherArabCurrencies}
          />
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
