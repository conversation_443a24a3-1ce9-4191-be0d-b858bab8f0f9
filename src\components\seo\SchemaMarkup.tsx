"use client";

interface SchemaMarkupProps {
  type: 'organization' | 'website' | 'breadcrumb' | 'calculator' | 'financialService';
  data?: any;
  lang?: 'en' | 'fr' | 'ar';
}

export function SchemaMarkup({ type, data, lang = 'en' }: SchemaMarkupProps) {
  const getSchemaData = () => {
    const baseUrl = 'https://calc.tolabi.net';
    
    switch (type) {
      case 'organization':
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": lang === 'ar' ? "حساب الفائدة (الربا)" : "RibaCalc",
          "description": lang === 'en'
            ? "Free Morocco loan calculator for MAD loans. Calculate fixed interest, monthly payments, and total repayment."
            : lang === 'fr'
            ? "Calculateur de prêt gratuit pour le Maroc en MAD. Calculez les intérêts fixes, paiements mensuels et remboursement total."
            : "حاسبة قروض مجانية للمغرب بالدرهم المغربي. احسب الفوائد الثابتة والدفعات الشهرية والسداد الإجمالي.",
          "url": baseUrl,
          "sameAs": [
            // Add your social media URLs here
            // "https://twitter.com/ribacalc",
            // "https://linkedin.com/company/ribacalc"
          ],
          "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "availableLanguage": ["English", "French", "Arabic"]
          },
          "areaServed": {
            "@type": "Country",
            "name": "Morocco"
          },
          "knowsAbout": [
            "Loan Calculations",
            "Islamic Banking",
            "Morocco Banking",
            "Interest Calculations",
            "Financial Planning",
            "MAD Loans"
          ]
        };

      case 'website':
        return {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": lang === 'ar' ? "حساب الفائدة (الربا)" : "RibaCalc",
          "description": lang === 'en'
            ? "Morocco's trusted loan calculator for MAD loans, Islamic banking, and financial planning"
            : lang === 'fr'
            ? "Calculateur de prêt de confiance du Maroc pour les prêts MAD, la banque islamique et la planification financière"
            : "حاسبة القروض الموثوقة في المغرب للقروض بالدرهم المغربي والبنوك الإسلامية والتخطيط المالي",
          "url": baseUrl,
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": `${baseUrl}/${lang}/blog?search={search_term_string}`
            },
            "query-input": "required name=search_term_string"
          },
          "inLanguage": lang === 'en' ? "en-US" : lang === 'fr' ? "fr-FR" : "ar-MA",
          "copyrightYear": new Date().getFullYear(),
          "copyrightHolder": {
            "@type": "Organization",
            "name": lang === 'ar' ? "حساب الفائدة (الربا)" : "RibaCalc"
          }
        };

      case 'calculator':
        return {
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": lang === 'ar' ? "حساب الفائدة (الربا) - حاسبة القروض المغرب" : "RibaCalc - Morocco Loan Calculator",
          "description": lang === 'en'
            ? "Free online calculator for Morocco loans in MAD. Calculate fixed interest, monthly payments, and total repayment amount."
            : lang === 'fr'
            ? "Calculateur en ligne gratuit pour les prêts Maroc en MAD. Calculez les intérêts fixes, les paiements mensuels et le montant total de remboursement."
            : "حاسبة مجانية عبر الإنترنت لقروض المغرب بالدرهم المغربي. احسب الفوائد الثابتة والدفعات الشهرية ومبلغ السداد الإجمالي.",
          "url": `${baseUrl}/${lang}`,
          "applicationCategory": "FinanceApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "MAD"
          },
          "featureList": [
            "Fixed Interest Calculation",
            "Monthly Payment Calculation",
            "Total Repayment Calculation",
            "MAD Currency Support",
            "Islamic Banking Compliance",
            "Multi-language Support"
          ],
          "softwareVersion": "1.0",
          "datePublished": "2025-01-01",
          "author": {
            "@type": "Organization",
            "name": lang === 'ar' ? "حساب الفائدة (الربا)" : "RibaCalc"
          }
        };

      case 'financialService':
        return {
          "@context": "https://schema.org",
          "@type": "FinancialService",
          "name": lang === 'ar' ? "خدمة حساب الفائدة (الربا) للقروض" : "RibaCalc Loan Calculator Service",
          "description": lang === 'en'
            ? "Professional loan calculation services for Morocco residents. Free, accurate, and Shariah-compliant calculations."
            : lang === 'fr'
            ? "Services de calcul de prêt professionnels pour les résidents du Maroc. Calculs gratuits, précis et conformes à la Charia."
            : "خدمات حساب القروض المهنية لسكان المغرب. حسابات مجانية ودقيقة ومتوافقة مع الشريعة.",
          "url": `${baseUrl}/${lang}`,
          "areaServed": {
            "@type": "Country",
            "name": "Morocco"
          },
          "serviceType": "Loan Calculation",
          "provider": {
            "@type": "Organization",
            "name": lang === 'ar' ? "حساب الفائدة (الربا)" : "RibaCalc"
          },
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Loan Calculation Services",
            "itemListElement": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Personal Loan Calculator",
                  "description": "Calculate personal loan payments and interest"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Mortgage Calculator",
                  "description": "Calculate home loan payments and interest"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Car Loan Calculator",
                  "description": "Calculate auto loan payments and interest"
                }
              }
            ]
          }
        };

      case 'breadcrumb':
        return {
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": data?.breadcrumbs?.map((item: any, index: number) => ({
            "@type": "ListItem",
            "position": index + 1,
            "name": item.name,
            "item": `${baseUrl}${item.url}`
          })) || []
        };

      default:
        return null;
    }
  };

  const schemaData = getSchemaData();

  if (!schemaData) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
    />
  );
}

// Helper component for multiple schema types
export function MultipleSchemaMarkup({ schemas }: { schemas: SchemaMarkupProps[] }) {
  return (
    <>
      {schemas.map((schema, index) => (
        <SchemaMarkup key={index} {...schema} />
      ))}
    </>
  );
}
