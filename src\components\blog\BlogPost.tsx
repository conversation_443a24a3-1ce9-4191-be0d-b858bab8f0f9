"use client";

import Link from "next/link";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Clock, ArrowLeft, Calculator, ArrowRight, HelpCircle } from "lucide-react";
import { BlogPost as BlogPostType, getRelatedPosts } from "@/lib/blog";
import LanguageSwitcher from "../LanguageSwitcher";
import { useEffect, useState } from "react";
import { SchemaMarkup } from "@/components/seo/SchemaMarkup";

interface BlogPostProps {
  dict: any;
  lang: 'en' | 'fr' | 'ar';
  post: BlogPostType;
}

export function BlogPost({ dict, lang, post }: BlogPostProps) {
  const [relatedPosts, setRelatedPosts] = useState<BlogPostType[]>([]);

  useEffect(() => {
    const fetchRelatedPosts = async () => {
      const related = await getRelatedPosts(post.slug, post.tags, lang);
      setRelatedPosts(related);
    };
    fetchRelatedPosts();
  }, [post.slug, post.tags, lang]);

  // Enhanced structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": post.title,
    "description": post.excerpt,
    "image": {
      "@type": "ImageObject",
      "url": `https://ribacalc.com/blog-images/${post.slug}.jpg`, // Replace with actual image URLs
      "width": 1200,
      "height": 630,
      "alt": post.title
    },
    "author": {
      "@type": "Organization",
      "name": post.author,
      "url": "https://ribacalc.com"
    },
    "publisher": {
      "@type": "Organization",
      "name": "RibaCalc",
      "url": "https://ribacalc.com"
    },
    "datePublished": post.publishedAt,
    "dateModified": post.publishedAt,
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `https://ribacalc.com/${lang}/blog/${post.slug}`
    },
    "url": `https://ribacalc.com/${lang}/blog/${post.slug}`,
    "keywords": post.tags.join(", "),
    "articleSection": "Finance",
    "articleBody": post.content.replace(/[#*`]/g, '').substring(0, 1000) + "...",
    "wordCount": post.content.split(' ').length,
    "timeRequired": `PT${post.readTime}M`,
    "inLanguage": lang === 'en' ? 'en-US' : 'fr-FR',
    "about": [
      {
        "@type": "Thing",
        "name": "UAE Banking",
        "description": "Banking services and regulations in the United Arab Emirates"
      },
      {
        "@type": "Thing",
        "name": "Loan Calculations",
        "description": "Mathematical calculations for loan interest and payments"
      },
      {
        "@type": "Thing",
        "name": "Islamic Finance",
        "description": "Shariah-compliant financial services and products"
      }
    ],
    "mentions": [
      {
        "@type": "Organization",
        "name": "UAE Central Bank",
        "description": "Central banking authority of the United Arab Emirates"
      },
      {
        "@type": "Place",
        "name": "United Arab Emirates",
        "description": "Country in the Middle East where the financial services are offered"
      }
    ],
    "isPartOf": {
      "@type": "Blog",
      "name": "RibaCalc Financial Blog",
      "url": `https://ribacalc.com/${lang}/blog`
    }
  };

  // Convert markdown-like content to HTML (basic implementation)
  const formatContent = (content: string) => {
    return content
      .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold mb-6 text-primary">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-semibold mb-4 mt-8">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-xl font-semibold mb-3 mt-6">$1</h3>')
      .replace(/^\*\*(.*)\*\*/gim, '<strong class="font-semibold">$1</strong>')
      .replace(/^\*(.*)\*/gim, '<em class="italic">$1</em>')
      .replace(/^\- (.*$)/gim, '<li class="ml-4">$1</li>')
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-primary hover:underline">$1</a>')
      .split('\n\n')
      .map((paragraph: string) => {
        if (paragraph.includes('<h1>') || paragraph.includes('<h2>') || paragraph.includes('<h3>') || paragraph.includes('<li>')) {
          return paragraph;
        }
        return `<p class="mb-4 leading-relaxed">${paragraph}</p>`;
      })
      .join('\n');
  };

  // Breadcrumb data for schema
  const breadcrumbData = {
    breadcrumbs: [
      { name: dict.navigation.home, url: `/${lang}` },
      { name: dict.blog.title, url: `/${lang}/blog` },
      { name: post.title, url: `/${lang}/blog/${post.slug}` }
    ]
  };

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <SchemaMarkup type="breadcrumb" data={breadcrumbData} lang={lang} />

      <div className="flex min-h-screen w-full flex-col bg-background pt-16">
      
      <div className="w-full max-w-4xl mx-auto px-4 sm:px-8 py-8">
        {/* Navigation */}
        <div className="mb-8">
          <Link 
            href={`/${lang}/blog`} 
            className="inline-flex items-center text-primary hover:text-primary/80"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {dict.blog.backToBlog}
          </Link>
        </div>

        {/* Article Header */}
        <article className="mb-12">
          <header className="mb-8">
            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
              <Clock className="h-4 w-4" />
              <span>{post.readTime} {dict.blog.readTime}</span>
              <span>•</span>
              <span>
                {dict.blog.publishedOn} {new Date(post.publishedAt).toLocaleDateString(lang === 'fr' ? 'fr-FR' : 'en-US')}
              </span>
            </div>
            
            <h1 className="text-4xl sm:text-5xl font-bold text-primary tracking-tight mb-4">
              {post.title}
            </h1>
            
            <p className="text-xl text-muted-foreground mb-6 leading-relaxed">
              {post.excerpt}
            </p>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {dict.blog.author} {post.author}
              </span>
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag: string) => (
                  <Badge key={tag} variant="secondary">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </header>

          <Separator className="mb-8" />

          {/* Article Content */}
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: formatContent(post.content) }}
          />
        </article>

        {/* Call to Action */}
        <Card className="mb-12 bg-primary/5 border-primary/20">
          <CardContent className="p-8 text-center">
            <Calculator className="mx-auto h-12 w-12 text-primary mb-4" />
            <h3 className="text-xl font-semibold mb-2">
              Ready to Calculate Your Loan?
            </h3>
            <p className="text-muted-foreground mb-4">
              Use our free calculator to see how these tips apply to your specific situation.
            </p>
            <div className="flex gap-4 justify-center">
              <Button asChild size="lg">
                <Link href={`/${lang}`}>
                  Try RibaCalc Now
                  <Calculator className="h-4 w-4 ml-2" />
                </Link>
              </Button>
              <Button variant="outline" asChild size="lg">
                <Link href={`/${lang}/faq`}>
                  Common Questions
                  <HelpCircle className="h-4 w-4 ml-2" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <section>
            <h2 className="text-2xl font-bold mb-8">{dict.blog.relatedPosts}</h2>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {relatedPosts.map((relatedPost: BlogPostType) => (
                <Card key={relatedPost.slug} className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                      <Clock className="h-4 w-4" />
                      <span>{relatedPost.readTime} {dict.blog.readTime}</span>
                    </div>
                    <CardTitle className="text-lg">
                      <Link 
                        href={`/${lang}/blog/${relatedPost.slug}`}
                        className="hover:text-primary transition-colors"
                      >
                        {relatedPost.title}
                      </Link>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground text-sm line-clamp-3 mb-4">
                      {relatedPost.excerpt}
                    </p>
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/${lang}/blog/${relatedPost.slug}`}>
                        {dict.blog.readMore}
                        <ArrowRight className="h-4 w-4 ml-1" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
    </>
  );
}
