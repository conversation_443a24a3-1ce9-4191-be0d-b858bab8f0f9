@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 208 100% 97%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 197 71% 60%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 120 73% 70%;
    --accent-foreground: 120 25% 15%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 197 71% 60%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 197 71% 73%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 120 73% 75%;
    --accent-foreground: 120 25% 15%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 197 71% 73%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Arabic font support - Enhanced */
  .font-arabic {
    font-family: 'Noto Sans Arabic', 'Amiri', 'Tajawal', 'Cairo', 'Inter', system-ui, -apple-system, sans-serif;
    font-weight: 400;
    line-height: 1.7;
    letter-spacing: 0.01em;
  }

  /* Arabic headings */
  .font-arabic h1, .font-arabic h2, .font-arabic h3, .font-arabic h4, .font-arabic h5, .font-arabic h6 {
    font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
    font-weight: 600;
    line-height: 1.4;
  }

  /* Arabic body text */
  .font-arabic p, .font-arabic span, .font-arabic div {
    line-height: 1.8;
    word-spacing: 0.1em;
  }
}



/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .btn-icon-right {
  margin-right: 0.5rem;
  margin-left: 0;
}

/* RTL specific adjustments - Fix flex direction issues */
[dir="rtl"] .flex:not(.flex-col):not(.flex-row) {
  flex-direction: row-reverse;
}

[dir="rtl"] .space-x-2 > * + * {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

/* Form inputs RTL */
[dir="rtl"] input[type="text"],
[dir="rtl"] input[type="number"],
[dir="rtl"] textarea,
[dir="rtl"] select {
  text-align: right;
  padding-left: 0.75rem;
  padding-right: 2.5rem;
  direction: rtl;
}

[dir="rtl"] .pl-10 {
  padding-left: 0.75rem;
  padding-right: 2.5rem;
}

[dir="rtl"] .pr-3 {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

/* Icon positioning in RTL */
[dir="rtl"] .absolute.left-3 {
  left: auto;
  right: 0.75rem;
}

[dir="rtl"] .absolute.right-3 {
  right: auto;
  left: 0.75rem;
}

/* Navigation RTL */
[dir="rtl"] .rotate-180 {
  transform: rotate(0deg);
}

[dir="rtl"] .rotate-0 {
  transform: rotate(180deg);
}

/* Grid and layout fixes for RTL */
[dir="rtl"] .grid {
  direction: rtl;
}

[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* Header and navigation fixes */
[dir="rtl"] .justify-between {
  direction: rtl;
}

[dir="rtl"] .items-center {
  direction: rtl;
}

/* Arabic numbers styling - Enhanced */
.arabic-numbers {
  font-feature-settings: "tnum";
  font-variant-numeric: tabular-nums;
  font-family: 'Cairo', 'Noto Sans Arabic', monospace;
  font-weight: 600;
  letter-spacing: 0.05em;
}

/* Enhanced Arabic UI */
[dir="rtl"] .arabic-numbers {
  font-family: "Cairo", "IBM Plex Sans Arabic", "Noto Sans Arabic", sans-serif;
  font-weight: 600;
  direction: ltr;
  text-align: left;
  display: inline-block;
}

/* Arabic text improvements */
[dir="rtl"] {
  font-size: 1.05em;
  line-height: 1.8;
}

/* Better spacing for Arabic cards */
[dir="rtl"] .card {
  padding: 1.5rem;
}

[dir="rtl"] .card-header {
  padding-bottom: 1.5rem;
}

[dir="rtl"] .card-content {
  padding-top: 0;
  padding-bottom: 1.5rem;
}

[dir="rtl"] .form-label {
  text-align: right;
  display: block;
  width: 100%;
}

[dir="rtl"] .input-wrapper {
  display: flex;
  flex-direction: row-reverse;
}

[dir="rtl"] .input-icon {
  margin-left: 0;
  margin-right: 8px;
}

/* Fix text alignment in cards for RTL */
[dir="rtl"] .text-left {
  text-align: right !important;
}

[dir="rtl"] .text-center {
  text-align: center !important;
}

/* Fix spacing and margins in RTL */
[dir="rtl"] .space-y-3 > * + * {
  margin-top: 0.75rem;
}

[dir="rtl"] .space-y-4 > * + * {
  margin-top: 1rem;
}

/* Fix button icons in RTL */
[dir="rtl"] .ml-2 {
  margin-left: 0 !important;
  margin-right: 0.5rem !important;
}

[dir="rtl"] .mr-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}

/* Fix card content alignment */
[dir="rtl"] .flex.justify-between {
  text-align: right;
}

[dir="rtl"] .flex.justify-between > *:first-child {
  text-align: right;
}

[dir="rtl"] .flex.justify-between > *:last-child {
  text-align: left;
}

/* Fix overlapping text in results - Enhanced */
[dir="rtl"] .flex.justify-between.items-center {
  gap: 1.5rem;
  min-height: 3rem;
  padding: 0.75rem 0;
}

[dir="rtl"] .flex-shrink-0 {
  flex-shrink: 0;
  max-width: 55%;
}

[dir="rtl"] .text-right {
  text-align: right !important;
  flex-shrink: 0;
  min-width: 40%;
  padding-left: 1rem;
}

/* Enhanced Arabic button styling */
[dir="rtl"] .btn {
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  border-radius: 0.5rem;
}

/* Arabic form improvements */
[dir="rtl"] .form-item {
  margin-bottom: 1.5rem;
}

[dir="rtl"] .form-label {
  font-weight: 600;
  margin-bottom: 0.75rem;
  font-size: 1.1em;
}

[dir="rtl"] input, [dir="rtl"] select, [dir="rtl"] textarea {
  padding: 0.875rem 1rem;
  font-size: 1.05em;
  border-radius: 0.5rem;
  border: 2px solid hsl(var(--border));
  transition: all 0.2s ease;
}

[dir="rtl"] input:focus, [dir="rtl"] select:focus, [dir="rtl"] textarea:focus {
  border-color: hsl(var(--primary));
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
}

/* Ensure proper spacing in Arabic layout */
[dir="rtl"] .w-full.space-y-3 {
  width: 100%;
}

[dir="rtl"] .w-full.space-y-3 > div {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
}

/* Fix header text overlap */
[dir="rtl"] .text-4xl,
[dir="rtl"] .text-5xl {
  line-height: 1.2;
  word-spacing: 0.1em;
}

/* Ensure proper text wrapping */
[dir="rtl"] .max-w-xs,
[dir="rtl"] .max-w-sm,
[dir="rtl"] .max-w-md {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Fix layout stability - prevent width changes */
@layer utilities {
  .grid-stable {
    grid-template-columns: 1fr;
  }

  @media (min-width: 768px) {
    .grid-stable {
      grid-template-columns: 1fr 1fr;
    }
  }

  /* Ensure cards maintain consistent width */
  .card-stable {
    flex: 1 1 0%;
    min-width: 0;
    width: 100%;
  }

  /* Prevent layout shift during calculations */
  .results-stable {
    min-height: 550px;
    transition: none;
  }

  /* Stable container width */
  .container-stable {
    max-width: 56rem;
    min-width: 0;
    width: 100%;
  }

  /* Arabic font styling - Enhanced */
  .font-arabic {
    font-family: 'Cairo', 'Amiri', 'Noto Sans Arabic', Arial, sans-serif;
    direction: rtl;
    line-height: 1.8;
  }

  /* RTL specific styles - Enhanced */
  [dir="rtl"] {
    text-align: right;
  }

  [dir="rtl"] .text-left {
    text-align: right;
  }

  [dir="rtl"] .text-right {
    text-align: left;
  }

  /* Arabic typography improvements */
  [dir="rtl"] h1 {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.3;
    margin-bottom: 1rem;
  }

  [dir="rtl"] h2 {
    font-size: 2rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.875rem;
  }

  [dir="rtl"] h3 {
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.4;
    margin-bottom: 0.75rem;
  }

  /* Enhanced card styling for Arabic */
  [dir="rtl"] .card {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    border-radius: 0.75rem;
    border: 1px solid hsl(var(--border));
  }

  [dir="rtl"] .card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.3s ease;
  }

  /* Better spacing for Arabic content */
  [dir="rtl"] .space-y-4 > * + * {
    margin-top: 1.25rem;
  }

  [dir="rtl"] .space-y-6 > * + * {
    margin-top: 1.75rem;
  }

  /* Arabic navigation improvements */
  [dir="rtl"] .nav-link {
    padding: 0.75rem 1.25rem;
    border-radius: 0.5rem;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  [dir="rtl"] .nav-link:hover {
    background-color: hsl(var(--accent));
    color: hsl(var(--accent-foreground));
  }

  /* Enhanced Arabic input styling */
  [dir="rtl"] input[type="number"] {
    text-align: left;
    direction: ltr;
    font-family: 'Cairo', 'Noto Sans Arabic', monospace;
    font-weight: 600;
  }

  [dir="rtl"] input[type="number"]::placeholder {
    text-align: right;
    direction: rtl;
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
    font-weight: 400;
  }

  /* Arabic select styling */
  [dir="rtl"] select {
    padding-right: 2.5rem;
    padding-left: 1rem;
  }

  /* Enhanced Arabic card headers */
  [dir="rtl"] .card-header h3 {
    font-size: 1.875rem;
    font-weight: 700;
    line-height: 1.3;
  }

  [dir="rtl"] .card-description {
    font-size: 1.125rem;
    line-height: 1.7;
    margin-top: 0.75rem;
  }

  /* Arabic button enhancements */
  [dir="rtl"] button {
    font-weight: 600;
    letter-spacing: 0.025em;
  }

  /* Arabic tooltip and dropdown improvements */
  [dir="rtl"] .tooltip, [dir="rtl"] .dropdown-content {
    text-align: right;
    font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
  }

  /* Enhanced Arabic spacing */
  [dir="rtl"] .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  @media (min-width: 640px) {
    [dir="rtl"] .container {
      padding-left: 2rem;
      padding-right: 2rem;
    }
  }

  /* Arabic responsive improvements */
  @media (max-width: 768px) {
    [dir="rtl"] h1 {
      font-size: 2rem;
      line-height: 1.2;
    }

    [dir="rtl"] h2 {
      font-size: 1.75rem;
      line-height: 1.3;
    }

    [dir="rtl"] .card {
      padding: 1.25rem;
    }

    [dir="rtl"] .card-header {
      padding-bottom: 1.25rem;
    }
  }

  /* Arabic color enhancements */
  [dir="rtl"] .text-primary {
    color: hsl(var(--primary));
    font-weight: 600;
  }

  [dir="rtl"] .text-muted-foreground {
    color: hsl(var(--muted-foreground));
    opacity: 0.9;
  }

  /* Arabic focus states */
  [dir="rtl"] input:focus, [dir="rtl"] select:focus, [dir="rtl"] textarea:focus {
    outline: none;
    ring: 2px solid hsl(var(--primary));
    ring-offset: 2px;
    border-color: hsl(var(--primary));
  }

  /* Arabic hover states */
  [dir="rtl"] .card:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }

  [dir="rtl"] button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
  }

  /* Arabic loading states */
  [dir="rtl"] .loading {
    opacity: 0.7;
    pointer-events: none;
  }

  /* Arabic success/error states */
  [dir="rtl"] .success {
    background-color: hsl(var(--success) / 0.1);
    border: 1px solid hsl(var(--success));
    color: hsl(var(--success-foreground));
  }

  [dir="rtl"] .error {
    background-color: hsl(var(--destructive) / 0.1);
    border: 1px solid hsl(var(--destructive));
    color: hsl(var(--destructive-foreground));
  }

  /* Arabic animation improvements */
  [dir="rtl"] .fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Arabic print styles */
  @media print {
    [dir="rtl"] {
      font-size: 12pt;
      line-height: 1.6;
    }

    [dir="rtl"] .card {
      break-inside: avoid;
      page-break-inside: avoid;
    }
  }
}
