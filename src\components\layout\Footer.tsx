"use client";

import Link from "next/link";
import { <PERSON><PERSON><PERSON>, BookO<PERSON>, HelpCircle, Heart } from "lucide-react";

interface FooterProps {
  dict: any;
  lang: 'en' | 'fr';
}

export function Footer({ dict, lang }: FooterProps) {
  const currentYear = new Date().getFullYear();

  const navigation = [
    {
      name: dict.navigation?.home || 'Calculator',
      href: `/${lang}`,
      icon: Calculator,
    },
    {
      name: dict.navigation?.blog || 'Blog',
      href: `/${lang}/blog`,
      icon: BookOpen,
    },
    {
      name: dict.navigation?.faq || 'FAQ',
      href: `/${lang}/faq`,
      icon: HelpCircle,
    }
  ];

  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <Link href={`/${lang}`} className="flex items-center space-x-2">
              <span className="text-xl font-bold text-primary">RibaCalc</span>
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <Calculator className="h-5 w-5" />
              </div>
            </Link>
            <p className="text-sm text-muted-foreground max-w-xs text-left">
              {dict.footer?.description || 'Free Morocco loan calculator for MAD loans. Calculate fixed interest, monthly payments, and total repayment.'}
            </p>
          </div>

          {/* Navigation */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold text-foreground text-left">
              {dict.footer?.quickLinks || 'Navigation'}
            </h3>
            <ul className="space-y-2">
              {navigation.map((item) => {
                const Icon = item.icon;
                return (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className="flex items-center space-x-2 text-left text-sm text-muted-foreground hover:text-primary transition-colors"
                    >
                      <span>{item.name}</span>
                      <Icon className="h-4 w-4" />
                    </Link>
                  </li>
                );
              })}
            </ul>
          </div>

          {/* Legal */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold text-foreground text-left">
              {dict.footer?.legal || 'Legal'}
            </h3>
            <ul className="space-y-2">
              <li>
                <span className="text-sm text-muted-foreground text-left">
                  {dict.footer?.educationalPurpose || 'Educational purposes only'}
                </span>
              </li>
              <li>
                <span className="text-sm text-muted-foreground text-left">
                  {dict.footer?.notFinancialAdvice || 'Not financial advice'}
                </span>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm text-muted-foreground text-left">
              © {currentYear} RibaCalc. {dict.footer?.allRightsReserved || 'All rights reserved'}.
            </p>
            <p className="text-sm text-muted-foreground flex items-center space-x-1">
              <span>{dict.footer?.madeWith || 'Made with'}</span>
              <Heart className="h-4 w-4 text-red-500" />
              <span>{dict.footer?.forMoroccoResidents || 'for Morocco residents'}</span>
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
