import type {Metadata, Viewport} from 'next';
import { Toaster } from "@/components/ui/toaster"
import { getDictionary } from '@/lib/dictionaries';
import { MultipleSchemaMarkup } from '@/components/seo/SchemaMarkup';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { FooterArabic } from '@/components/layout/FooterArabic';
import { HtmlLangSetter } from '@/components/layout/HtmlLangSetter';

type LayoutProps = {
  children: React.ReactNode;
  params: Promise<{ lang: 'en' | 'fr' | 'ar' }>;
};

export async function generateMetadata({ params }: LayoutProps): Promise<Metadata> {
  const { lang } = await params;
  const dict = await getDictionary(lang);
  return {
    title: {
      template: `%s | ${dict.seo.title}`,
      default: dict.seo.title,
    },
    description: dict.seo.description,
    keywords: [
      'loan calculator',
      'interest calculator',
      'fixed interest',
      'Islamic banking',
      'Riba calculator',
      'mortgage calculator',
      'financial planning',
      'Arab countries banking',
      'UAE loans',
      'Saudi Arabia financing',
      'Egypt banking',
      'Kuwait loans',
      'Qatar banking',
      'Bahrain finance',
      'Oman loans',
      'Jordan banking',
      'Morocco loans',
      'Tunisia banking',
      'Algeria finance',
      'Lebanon banking',
      'Iraq loans',
      'Syria banking',
      'Yemen finance',
      'Libya banking',
      'Sudan loans',
      'AED calculator',
      'SAR calculator',
      'EGP calculator',
      'KWD calculator',
      'QAR calculator',
      'BHD calculator',
      'OMR calculator',
      'JOD calculator',
      'MAD calculator',
      'TND calculator',
      'DZD calculator',
      'LBP calculator',
      'IQD calculator',
      'SYP calculator',
      'YER calculator',
      'LYD calculator',
      'SDG calculator'
    ],
    authors: [{ name: 'RibaCalc Team' }],
    creator: 'RibaCalc',
    publisher: 'RibaCalc',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL('https://calc.tolabi.net'),
    alternates: {
      canonical: `/${lang}`,
      languages: {
        'en': '/en',
        'fr': '/fr',
        'ar': '/ar',
      },
    },
    openGraph: {
      title: dict.seo.title,
      description: dict.seo.description,
      url: `https://calc.tolabi.net/${lang}`,
      siteName: 'RibaCalc',
      locale: lang === 'en' ? 'en_US' : lang === 'fr' ? 'fr_FR' : 'ar_MA',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: dict.seo.title,
      description: dict.seo.description,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    verification: {
      // Add your Google Search Console verification code here when available
      // google: 'your-google-verification-code',
    },
  };
}

export function generateViewport(): Viewport {
  return {
    width: 'device-width',
    initialScale: 1,
  };
}

export default async function LangLayout({
  children,
  params,
}: Readonly<LayoutProps>) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <div dir={lang === 'ar' ? 'rtl' : 'ltr'} className={lang === 'ar' ? 'font-arabic' : ''}>
      <HtmlLangSetter lang={lang} />
      <MultipleSchemaMarkup
        schemas={[
          { type: 'organization', lang },
          { type: 'website', lang },
          { type: 'financialService', lang }
        ]}
      />
      <Header dict={dict} lang={lang} />
      <main className="min-h-screen">
        {children}
      </main>
      {lang === 'ar' ? (
        <FooterArabic dict={dict} />
      ) : (
        <Footer dict={dict} lang={lang} />
      )}
      <Toaster />
    </div>
  );
}
