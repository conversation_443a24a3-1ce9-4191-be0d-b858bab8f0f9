// Currency formatting utilities for Arab countries

export interface Currency {
  code: string;
  name: {
    en: string;
    fr: string;
    ar: string;
  };
  symbol: string;
  country: {
    en: string;
    fr: string;
    ar: string;
  };
  countryCode: string;
  locale: {
    en: string;
    fr: string;
    ar: string;
  };
}

export const arabCurrencies: Record<string, Currency> = {
  AED: {
    code: 'AED',
    name: {
      en: 'UAE Dirham',
      fr: '<PERSON>rham des EAU',
      ar: 'درهم إماراتي'
    },
    symbol: 'د.إ',
    country: {
      en: 'United Arab Emirates',
      fr: 'Émirats arabes unis',
      ar: 'الإمارات العربية المتحدة'
    },
    countryCode: 'AE',
    locale: {
      en: 'en-AE',
      fr: 'fr-AE',
      ar: 'ar-AE'
    }
  },
  SAR: {
    code: 'SAR',
    name: {
      en: 'Saudi Riyal',
      fr: 'Riyal saoudien',
      ar: 'ريال سعودي'
    },
    symbol: 'ر.س',
    country: {
      en: 'Saudi Arabia',
      fr: 'Arabie saoudite',
      ar: 'المملكة العربية السعودية'
    },
    countryCode: 'SA',
    locale: {
      en: 'en-SA',
      fr: 'fr-SA',
      ar: 'ar-SA'
    }
  },
  EGP: {
    code: 'EGP',
    name: {
      en: 'Egyptian Pound',
      fr: 'Livre égyptienne',
      ar: 'جنيه مصري'
    },
    symbol: 'ج.م',
    country: {
      en: 'Egypt',
      fr: 'Égypte',
      ar: 'مصر'
    },
    countryCode: 'EG',
    locale: {
      en: 'en-EG',
      fr: 'fr-EG',
      ar: 'ar-EG'
    }
  },
  KWD: {
    code: 'KWD',
    name: {
      en: 'Kuwaiti Dinar',
      fr: 'Dinar koweïtien',
      ar: 'دينار كويتي'
    },
    symbol: 'د.ك',
    country: {
      en: 'Kuwait',
      fr: 'Koweït',
      ar: 'الكويت'
    },
    countryCode: 'KW',
    locale: {
      en: 'en-KW',
      fr: 'fr-KW',
      ar: 'ar-KW'
    }
  },
  QAR: {
    code: 'QAR',
    name: {
      en: 'Qatari Riyal',
      fr: 'Riyal qatarien',
      ar: 'ريال قطري'
    },
    symbol: 'ر.ق',
    country: {
      en: 'Qatar',
      fr: 'Qatar',
      ar: 'قطر'
    },
    countryCode: 'QA',
    locale: {
      en: 'en-QA',
      fr: 'fr-QA',
      ar: 'ar-QA'
    }
  },
  BHD: {
    code: 'BHD',
    name: {
      en: 'Bahraini Dinar',
      fr: 'Dinar bahreïni',
      ar: 'دينار بحريني'
    },
    symbol: 'د.ب',
    country: {
      en: 'Bahrain',
      fr: 'Bahreïn',
      ar: 'البحرين'
    },
    countryCode: 'BH',
    locale: {
      en: 'en-BH',
      fr: 'fr-BH',
      ar: 'ar-BH'
    }
  },
  OMR: {
    code: 'OMR',
    name: {
      en: 'Omani Rial',
      fr: 'Rial omanais',
      ar: 'ريال عماني'
    },
    symbol: 'ر.ع',
    country: {
      en: 'Oman',
      fr: 'Oman',
      ar: 'عمان'
    },
    countryCode: 'OM',
    locale: {
      en: 'en-OM',
      fr: 'fr-OM',
      ar: 'ar-OM'
    }
  },
  JOD: {
    code: 'JOD',
    name: {
      en: 'Jordanian Dinar',
      fr: 'Dinar jordanien',
      ar: 'دينار أردني'
    },
    symbol: 'د.أ',
    country: {
      en: 'Jordan',
      fr: 'Jordanie',
      ar: 'الأردن'
    },
    countryCode: 'JO',
    locale: {
      en: 'en-JO',
      fr: 'fr-JO',
      ar: 'ar-JO'
    }
  },
  MAD: {
    code: 'MAD',
    name: {
      en: 'Moroccan Dirham',
      fr: 'Dirham marocain',
      ar: 'درهم مغربي'
    },
    symbol: 'د.م',
    country: {
      en: 'Morocco',
      fr: 'Maroc',
      ar: 'المغرب'
    },
    countryCode: 'MA',
    locale: {
      en: 'en-MA',
      fr: 'fr-MA',
      ar: 'ar-MA'
    }
  },
  TND: {
    code: 'TND',
    name: {
      en: 'Tunisian Dinar',
      fr: 'Dinar tunisien',
      ar: 'دينار تونسي'
    },
    symbol: 'د.ت',
    country: {
      en: 'Tunisia',
      fr: 'Tunisie',
      ar: 'تونس'
    },
    countryCode: 'TN',
    locale: {
      en: 'en-TN',
      fr: 'fr-TN',
      ar: 'ar-TN'
    }
  },
  DZD: {
    code: 'DZD',
    name: {
      en: 'Algerian Dinar',
      fr: 'Dinar algérien',
      ar: 'دينار جزائري'
    },
    symbol: 'د.ج',
    country: {
      en: 'Algeria',
      fr: 'Algérie',
      ar: 'الجزائر'
    },
    countryCode: 'DZ',
    locale: {
      en: 'en-DZ',
      fr: 'fr-DZ',
      ar: 'ar-DZ'
    }
  },
  LBP: {
    code: 'LBP',
    name: {
      en: 'Lebanese Pound',
      fr: 'Livre libanaise',
      ar: 'ليرة لبنانية'
    },
    symbol: 'ل.ل',
    country: {
      en: 'Lebanon',
      fr: 'Liban',
      ar: 'لبنان'
    },
    countryCode: 'LB',
    locale: {
      en: 'en-LB',
      fr: 'fr-LB',
      ar: 'ar-LB'
    }
  },
  IQD: {
    code: 'IQD',
    name: {
      en: 'Iraqi Dinar',
      fr: 'Dinar irakien',
      ar: 'دينار عراقي'
    },
    symbol: 'د.ع',
    country: {
      en: 'Iraq',
      fr: 'Irak',
      ar: 'العراق'
    },
    countryCode: 'IQ',
    locale: {
      en: 'en-IQ',
      fr: 'fr-IQ',
      ar: 'ar-IQ'
    }
  },
  SYP: {
    code: 'SYP',
    name: {
      en: 'Syrian Pound',
      fr: 'Livre syrienne',
      ar: 'ليرة سورية'
    },
    symbol: 'ل.س',
    country: {
      en: 'Syria',
      fr: 'Syrie',
      ar: 'سوريا'
    },
    countryCode: 'SY',
    locale: {
      en: 'en-SY',
      fr: 'fr-SY',
      ar: 'ar-SY'
    }
  },
  YER: {
    code: 'YER',
    name: {
      en: 'Yemeni Rial',
      fr: 'Rial yéménite',
      ar: 'ريال يمني'
    },
    symbol: 'ر.ي',
    country: {
      en: 'Yemen',
      fr: 'Yémen',
      ar: 'اليمن'
    },
    countryCode: 'YE',
    locale: {
      en: 'en-YE',
      fr: 'fr-YE',
      ar: 'ar-YE'
    }
  },
  LYD: {
    code: 'LYD',
    name: {
      en: 'Libyan Dinar',
      fr: 'Dinar libyen',
      ar: 'دينار ليبي'
    },
    symbol: 'د.ل',
    country: {
      en: 'Libya',
      fr: 'Libye',
      ar: 'ليبيا'
    },
    countryCode: 'LY',
    locale: {
      en: 'en-LY',
      fr: 'fr-LY',
      ar: 'ar-LY'
    }
  },
  SDG: {
    code: 'SDG',
    name: {
      en: 'Sudanese Pound',
      fr: 'Livre soudanaise',
      ar: 'جنيه سوداني'
    },
    symbol: 'ج.س',
    country: {
      en: 'Sudan',
      fr: 'Soudan',
      ar: 'السودان'
    },
    countryCode: 'SD',
    locale: {
      en: 'en-SD',
      fr: 'fr-SD',
      ar: 'ar-SD'
    }
  },
  USD: {
    code: 'USD',
    name: {
      en: 'US Dollar',
      fr: 'Dollar américain',
      ar: 'دولار أمريكي'
    },
    symbol: '$',
    country: {
      en: 'United States',
      fr: 'États-Unis',
      ar: 'الولايات المتحدة'
    },
    countryCode: 'US',
    locale: {
      en: 'en-US',
      fr: 'fr-US',
      ar: 'ar-US'
    }
  },
  EUR: {
    code: 'EUR',
    name: {
      en: 'Euro',
      fr: 'Euro',
      ar: 'يورو'
    },
    symbol: '€',
    country: {
      en: 'European Union',
      fr: 'Union européenne',
      ar: 'الاتحاد الأوروبي'
    },
    countryCode: 'EU',
    locale: {
      en: 'en-EU',
      fr: 'fr-EU',
      ar: 'ar-EU'
    }
  }
};

export const formatCurrency = (value: number, currencyCode: string = 'MAD', locale: string = 'en') => {
  const currency = arabCurrencies[currencyCode] || arabCurrencies.MAD;
  const localeCode = currency.locale[locale as keyof typeof currency.locale] || currency.locale.en;

  try {
    return new Intl.NumberFormat(localeCode, {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  } catch (error) {
    // Fallback formatting if Intl.NumberFormat fails
    const symbol = currency.symbol;
    const formattedNumber = formatNumber(value, locale);
    return locale === 'ar' ? `${formattedNumber} ${symbol}` : `${symbol} ${formattedNumber}`;
  }
};

export const formatNumber = (value: number, locale: string = 'en') => {
  // For Arabic locale, use Arabic-Indic numerals
  if (locale === 'ar') {
    return new Intl.NumberFormat('ar', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value);
  }

  // For French locale
  if (locale === 'fr') {
    return new Intl.NumberFormat('fr', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value);
  }

  // Default English formatting
  return new Intl.NumberFormat('en', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(value);
};

export const getCurrencyList = (): Currency[] => {
  return Object.values(arabCurrencies);
};

export const getCurrency = (code: string): Currency | null => {
  return arabCurrencies[code] || null;
};

export const getDefaultCurrency = (): string => {
  return 'MAD'; // Default to Moroccan Dirham
};


