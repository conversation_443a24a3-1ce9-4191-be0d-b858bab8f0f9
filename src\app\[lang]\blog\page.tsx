import { getDictionary } from '@/lib/dictionaries'
import { BlogList } from '@/components/blog/BlogList'
import { getBlogPosts } from '@/lib/blog'

type PageProps = {
    params: Promise<{ lang: 'en' | 'fr' | 'ar' }>;
};

export default async function BlogPage({ params }: PageProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);
  const posts = await getBlogPosts(lang);
  
  return <BlogList dict={dict} lang={lang} posts={posts} />
}

export async function generateMetadata({ params }: PageProps) {
  const { lang } = await params;
  const dict = await getDictionary(lang);
  
  return {
    title: `${dict.blog.title} | ${dict.seo.title}`,
    description: dict.blog.description,
    alternates: {
      canonical: `/${lang}/blog`,
      languages: {
        'en': '/en/blog',
        'fr': '/fr/blog',
        'ar': '/ar/blog',
      },
    },
  };
}
